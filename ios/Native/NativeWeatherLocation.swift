//
//  NativeWeatherLocation.swift
//  WeatherApp
//
//  Created by <PERSON> on 2025/7/16.
//

import Foundation
import React
import CoreLocation

// Define the promise block types
typealias RCTPromiseResolveBlock = (Any?) -> Void
typealias RCTPromiseRejectBlock = (String?, String?, Error?) -> Void

@objc(NativeWeatherLocation)
class NativeWeatherLocation: NSObject, RCTBridgeModule, CLLocationManagerDelegate {
  private let locationManager = CLLocationManager()
  private var currentPromiseResolve: RCTPromiseResolveBlock?
  private var currentPromiseReject: RCTPromiseRejectBlock?
  
  override init() {
    super.init()
    locationManager.delegate = self
    locationManager.desiredAccuracy = kCLLocationAccuracyBest
  }
  
  @objc
  func getCurrentCity(_ resolve: @escaping RCTPromiseResolveBlock, reject: @escaping RCTPromiseRejectBlock) {
    currentPromiseResolve = resolve
    currentPromiseReject = reject
    
    // Check authorization status
    let authStatus = locationManager.authorizationStatus
    
    switch authStatus {
    case .notDetermined:
      locationManager.requestWhenInUseAuthorization()
    case .denied, .restricted:
      reject("LOCATION_PERMISSION_DENIED", "Location permission denied", nil)
    case .authorizedWhenInUse, .authorizedAlways:
      requestLocation()
    @unknown default:
      reject("LOCATION_UNKNOWN_ERROR", "Unknown location authorization status", nil)
    }
  }
  
  private func requestLocation() {
    locationManager.requestLocation()
  }
  
  // MARK: - CLLocationManagerDelegate
  
  func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
    guard let location = locations.first else {
      currentPromiseReject?("LOCATION_ERROR", "No location found", nil)
      return
    }
    
    let geocoder = CLGeocoder()
    geocoder.reverseGeocodeLocation(location) { [weak self] placemarks, error in
      if let error = error {
        self?.currentPromiseReject?("GEOCODING_ERROR", error.localizedDescription, error)
        return
      }
      
      guard let placemark = placemarks?.first,
            let city = placemark.locality else {
        self?.currentPromiseReject?("CITY_NOT_FOUND", "Could not determine city name", nil)
        return
      }
      
      self?.currentPromiseResolve?(city)
    }
  }
  
  func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
    currentPromiseReject?("LOCATION_ERROR", error.localizedDescription, error)
  }
  
  func locationManager(_ manager: CLLocationManager, didChangeAuthorization status: CLAuthorizationStatus) {
    switch status {
    case .authorizedWhenInUse, .authorizedAlways:
      requestLocation()
    case .denied, .restricted:
      currentPromiseReject?("LOCATION_PERMISSION_DENIED", "Location permission denied", nil)
    case .notDetermined:
      // Wait for user decision
      break
    @unknown default:
      currentPromiseReject?("LOCATION_UNKNOWN_ERROR", "Unknown location authorization status", nil)
    }
  }
  
  @objc
  static func moduleName() -> String! {
    return "NativeWeatherLocation"
  }

  @objc
  static func requiresMainQueueSetup() -> Bool {
    return false
  }

  @objc
  func getTurboModule(_ params: Any!) -> Any! {
    return self
  }
}
