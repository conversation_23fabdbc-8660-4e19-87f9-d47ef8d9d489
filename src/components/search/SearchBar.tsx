import { View, TextInput, Button, TouchableOpacity, Text } from 'react-native';
import searchStyles from '../../styles/search';
import { useState } from 'react';

type  SearchBarProps = {
  searchTextUpdated: (text: string) => void;
};

export default function SearchBar({ searchTextUpdated }: SearchBarProps) {
  const [search, setSearch] = useState("");
  return (
    <View style={searchStyles.searchContainer}>
      <TextInput
        style={searchStyles.input}
        placeholder="Search"
        value={search}
        onChangeText={(text) => {
          setSearch(text)
          searchTextUpdated(text)
        }}
      />
      <TouchableOpacity style={searchStyles.button} onPress={() => {
        searchTextUpdated(search);
      }}>
        <Text style={searchStyles.text}>Search</Text>
      </TouchableOpacity>
    </View>
  );
}
