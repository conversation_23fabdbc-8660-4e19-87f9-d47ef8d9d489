import { createStackNavigator } from '@react-navigation/stack';
import { NavigationContainer } from '@react-navigation/native';

import HomeScreen from '../home/<USER>';
import FavoriteScreen from '../favorite/FavoriteScreen';
import { WeatherParamList } from '../../data/WeatherParamList';
import Loading from '../common/Loading';
import React, { Suspense } from 'react';
export default function WeatherNavigator() {
  const Stack = createStackNavigator<WeatherParamList>();
  return (
    <NavigationContainer>
      <Stack.Navigator initialRouteName="HomeScreen">
        <Stack.Screen
          name="HomeScreen"
          options={{
            title: 'Home',
            headerTintColor: '#191919',
            headerStyle: {
              backgroundColor: '#fff',
            },
          }}
        >
          {(props: any) => <Suspense fallback={<Loading />}>
            <HomeScreen {...props} location="New York" />
          </Suspense>}
        </Stack.Screen>
        <Stack.Screen
          name="Favorite"
          options={{
            title: 'My Favorites',
            headerTintColor: '#191919',
            headerStyle: {
              backgroundColor: '#fff',
            },
          }}
        >
          {(props: any) => <Suspense fallback={<Loading />}>
            <FavoriteScreen {...props} />
          </Suspense>}
        </Stack.Screen>
      </Stack.Navigator>
    </NavigationContainer>
  );
}
