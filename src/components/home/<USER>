import { View, Text, FlatList } from 'react-native';
import homeStyles from '../../styles/home';
import { WeatherList } from '../../data/ForcastCardProps';

type ForcastCardProps = {
  weather: WeatherList;
};
export default function ForcastCard({ weather }: ForcastCardProps) {
  console.log('ForcastCard');
  console.log(weather);

  return (
    <View style={homeStyles.forcastCard}>
      <Text>{weather.dt_txt}</Text>
      {weather.weather && weather.weather.length > 0 && (
        <Text style={homeStyles.forecastDes}>
          {weather.weather[0].description}
        </Text>
      )}
    </View>
  );
}

type ForcastListProps = {
  weatherList: WeatherList[];
};
export function ForcastList({ weatherList }: ForcastListProps) {
  return (
    <FlatList
      style={{
        ...homeStyles.forecastList,
      }}
      data={weatherList}
      horizontal
      renderItem={({ item }) => <ForcastCard weather={item} />}
      keyExtractor={item => item.dt.toString()}
    />
  );
}
