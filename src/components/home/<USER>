import { View, Text, FlatList, TouchableOpacity } from 'react-native'
import { useEffect, useState } from 'react'
import homeStyles from '../../styles/home'

type CityListProps = {
    city: string,
    onCitySelected: (city: string) => void
}

type City = {
    id: number
    name: string
}

const allCities: City[] = [
    {
        id: 1,
        name: 'New York',
    },
    {
        id: 2,
        name: 'Los Angeles',
    },
    {
        id: 3,
        name: 'Chicago',
    },
    {
        id: 4,
        name: 'Houston',
    },
    {
        id: 5,
        name: 'Miami',
    },
]

export default function FilterCityList({ city, onCitySelected }: CityListProps) {

    const [cities, setCities] = useState<City[]>(allCities)
    useEffect(() => {
        if (city) {
            setCities(allCities.filter(item => item.name.toLowerCase().includes(city.toLowerCase())))
        } else {
            setCities(allCities)
        }
    }, [city])

    return (
        <FlatList
            data={cities}
            renderItem={({ item }) => <CityItem city={item} onCitySelected={onCitySelected} />}
            keyExtractor={item => item.id.toString()}
            style={homeStyles.cityList}
        />
    )
}

function CityItem({ city, onCitySelected }: { city: City, onCitySelected: (city: string) => void }) {
    return (
        <TouchableOpacity onPress={() => onCitySelected(city.name)}>
        <View style={homeStyles.cityItem}>
            <Text style={homeStyles.cityItemText}>{city.name}</Text>
            <TouchableOpacity style={homeStyles.cityItemButton} onPress={() => {}}>
                <Text style={homeStyles.cityItemButtonText}>Add to Favorite</Text>
            </TouchableOpacity>
        </View>
        </TouchableOpacity>
    )
}   