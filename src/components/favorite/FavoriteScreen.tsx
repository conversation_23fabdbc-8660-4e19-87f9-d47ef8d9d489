import { <PERSON>, <PERSON>, <PERSON><PERSON>, FlatList, Alert } from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { WeatherParamList } from '../../data/WeatherParamList';
import { useState, useEffect } from 'react';
import NativeWeatherStorage from '../../modules/NativeWeatherStorage';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../state/store';
import favoriteStyles from '../../styles/favorite';

type FavoriteProps = NativeStackScreenProps<WeatherParamList, "Favorite">;
export default function FavoriteScreen({ route }: FavoriteProps) {

  const favoriteList = useSelector((state: RootState) => state.favorite.value);
  const renderFavoriteItem = ({ item }: { item: string }) => (
    <View style={favoriteStyles.favoriteItem}>
      <Text>{item}</Text>
    </View>
  );

  return (
    <View style={favoriteStyles.container}>
      <Text style={favoriteStyles.favoriteHeader}>
        My Favorites ({favoriteList.length})
      </Text>
      <FlatList
          data={favoriteList}
          renderItem={renderFavoriteItem}
          keyExtractor={(item) => item}
        />
    </View>
  );
}
