import { useState, useEffect } from 'react';
import { GET } from './httpClient';
import WeatherResponse from 'src/data/ForcastCardProps';

const useWeatherRequest = (city: string) => {
  const [weather, setWeather] = useState<WeatherResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    setLoading(true);
    setError(null);
    setWeather(null);

    GET(`/forecast?q=${city}`, {})
      .then(response => {
        setWeather(response  as WeatherResponse);
      })
      .catch(err => {
        setError(err);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [city]);
  return { weather, loading, error };
};

export default useWeatherRequest;
