
// const API_KEY = '083ae251da42d90fcede071e7994c289';
const API_KEY = '083ae251da42d90fcede071e7994c288';

// const getSSLConfig = () => {
//   return {
//     pkPinning: true, 
//     sslPinning: {
//       certs: [
//         'sha256/UByNN6wh6WJFNj04mBWbj+iAfJP3C60LXpHBZXmMXwk='
//       ],
//     },
//   };
// };

const BASE_URL = 'https://api.openweathermap.org/data/2.5';
const DEFAULT_TIMEOUT = 15000;

interface RequestConfig {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  body?: string;
  timeout?: number;
}

class HttpClient {
  private baseURL: string;
  private defaultHeaders: Record<string, string>;
  private timeout: number;

  constructor(baseURL: string, timeout: number = DEFAULT_TIMEOUT) {
    this.baseURL = baseURL;
    this.timeout = timeout;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
    };
  }

  private buildUrl(endpoint: string): string {
    const url = `${this.baseURL}${endpoint}`;
    const separator = url.includes('?') ? '&' : '?';
    return `${url}${separator}appid=${API_KEY}`;
  }

  private async makeRequest<T>(endpoint: string, config: RequestConfig = {}): Promise<T> {
    const url = this.buildUrl(endpoint);

    const requestConfig = {
      method: config.method || 'GET',
      headers: {
        ...this.defaultHeaders,
        ...config.headers,
      },
      body: config.body,
      timeoutInterval: config.timeout || this.timeout,
      // ...sslConfig,
    };

    try {
      const response = await fetch(url, requestConfig);
      if (response.status < 200 || response.status >= 300) {
        throw new Error(`unexpected response status: ${response.status}`);
      }
      const data = await response.json();
      console.log(data);
      return data as T;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async get<T>(endpoint: string, config: RequestConfig = {}): Promise<T> {
    return this.makeRequest<T>(endpoint, { ...config, method: 'GET' });
  }

  async post<T>(endpoint: string, data?: any, config: RequestConfig = {}): Promise<T> {
    const body = data ? JSON.stringify(data) : undefined;
    return this.makeRequest<T>(endpoint, { ...config, method: 'POST', body });
  }
}

const httpClient = new HttpClient(BASE_URL);

function GET<T>(url: string, config: RequestConfig = {}): Promise<T> {
  return httpClient.get<T>(url, config);
}

function POST<T>(url: string, data?: any, config: RequestConfig = {}): Promise<T> {
  return httpClient.post<T>(url, data, config);
}


export default httpClient;
export { GET, POST };
