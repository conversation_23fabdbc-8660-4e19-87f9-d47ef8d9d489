import { StyleSheet } from "react-native";

const searchStyles = StyleSheet.create({
    container: {
        width: '100%',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#f2f3f5',
        marginTop: 10,
        flexDirection: 'row',
        paddingBottom:0,
      },
      searchContainer: {
        width: '90%',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#f2f3f5',
        marginTop: 10,
        flexDirection: 'row',
        paddingBottom: 10,
      },
      input: {
        width: '70%',
        height: 40,
        borderColor: 'gray',
        borderWidth: 1,
        borderRadius: 10,
        paddingHorizontal: 10,
        marginRight: 10,
      },
      button: {
        width: '30%',
        height: 40,
        fontSize: 16,
        justifyContent: 'center',
      },
      text: {
        fontSize: 16,
        color: '#064e85',
      },

});

export default searchStyles;