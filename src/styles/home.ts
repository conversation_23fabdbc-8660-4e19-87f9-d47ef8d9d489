import { StyleSheet } from "react-native";


const homeStyles = StyleSheet.create({
    container: {
        width: '100%',
        height: '100%',
        justifyContent: 'flex-start',
        alignItems: 'center',
        backgroundColor: '#f2f3f5',
        marginTop: 30,
      },
      homeHeader: { fontSize: 18, fontWeight: 'bold', marginBottom: 10 },
      forcastContainer: {
        width: '100%',
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#f2f3f5',
      },
      forecastList: {
        width: '100%',
      },
      forcastCard: {
        paddingVertical: 10,
        paddingHorizontal: 20,
        height: 100,
        backgroundColor: '#fff',
        borderRadius: 10,
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
        marginVertical: 10,
        marginHorizontal: 10,
      },
      forecastDes: {
        paddingTop: 10,
      },
      cityList: {
        width: '100%',
        paddingHorizontal: 20,
      },
      cityItem: {
        width: '100%',
        paddingVertical: 10,
        height: 60,
        backgroundColor: '#fff',
        borderRadius: 10,
        marginVertical: 10,
        flexDirection: 'row',
        justifyContent: 'space-between',
      },
      cityItemText: {
        fontSize: 16,
        color: '#191919',
        marginLeft: 10,
        marginTop: 10,
      },
      cityItemButton: {
        height: 60,
        marginRight: 10,
      },
      cityItemButtonText: {
        fontSize: 16,
        color: '#f00',
        marginLeft: 10,
        marginTop: 10,
      },
      errorText: {
        color: '#f00',
        marginVertical: 40,
      },
      contentContainer: {
        width: '100%',
        paddingHorizontal: 20,
      }
});

export default homeStyles;