import { createSlice, PayloadAction } from '@reduxjs/toolkit'

interface FavoriteState {
  value: string[]
}

const initialState: FavoriteState = {
  value: [],
}

const favoriteSlice = createSlice({
  name: 'favorite',
  initialState,
  reducers: {
    addFavorite: (state, action: PayloadAction<string>) => {
      if (!state.value.includes(action.payload)) {
        state.value.push(action.payload)
      }
    },
    removeFavorite: (state, action: PayloadAction<string>) => {
      state.value = state.value.filter(item => item !== action.payload)
    }
  }
})

export const { addFavorite, removeFavorite } = favoriteSlice.actions

export default favoriteSlice.reducer