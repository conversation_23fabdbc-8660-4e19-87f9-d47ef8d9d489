import React from 'react';
import { Provider } from 'react-redux';
import { store } from './src/state/store';
import WeatherNavigator from './src/components/navigator/WeatherNavigator';
import { RootSiblingParent } from 'react-native-root-siblings';

function App() {
  return (
    <RootSiblingParent>
      <Provider store={store}>
        <WeatherNavigator />
      </Provider>
    </RootSiblingParent>
  );
}

export default App;
