// const obfuscatingTransformer = require('react-native-obfuscating-transformer').transform;

// module.exports = obfuscatingTransformer({});


const obfuscatingTransformer = require("react-native-obfuscating-transformer");

const filter = (filename) => {
  return filename.startsWith("src");
};

module.exports = obfuscatingTransformer({
  obfuscatorOptions: {
    compact: true,
    controlFlowFlattening: false,
    controlFlowFlatteningThreshold: 0.75,
    deadCodeInjection: false,
    deadCodeInjectionThreshold: 0.4,
    debugProtection: false,
    debugProtectionInterval: true,
    disableConsoleOutput: false,
    domainLock: [],
    identifierNamesGenerator: "mangled",
    identifiersDictionary: [],
    identifiersPrefix: "",
    inputFileName: "",
    log: true,
    renameGlobals: false,
    renameProperties: false,
    reservedNames: [],
    reservedStrings: [],
    rotateStringArray: true,
    seed: 0,
    selfDefending: true,
    shuffleStringArray: true,
    sourceMap: false,
    sourceMapBaseUrl: "",
    sourceMapFileName: "",
    sourceMapMode: "separate",
    splitStrings: false,
    splitStringsChunkLength: 10,
    stringArray: true,
    stringArrayEncoding: false,
    stringArrayThreshold: 0.75,
    target: "node",
    transformObjectKeys: true,
    unicodeEscapeSequence: false,
  },
  upstreamTransformer: require("metro-react-native-babel-transformer"),
  emitObfuscatedFiles: false,
  enableInDevelopment: true,
  filter,
  trace: true,
});