{"name": "weather-app", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@react-native/new-app-screen": "0.80.1", "@react-navigation/native": "^7.1.14", "@react-navigation/native-stack": "^7.3.21", "@react-navigation/stack": "^7.4.2", "@reduxjs/toolkit": "^2.8.2", "react": "19.1.0", "react-native": "0.80.1", "react-native-gesture-handler": "^2.27.1", "react-native-reanimated": "^3.18.0", "react-native-root-toast": "^4.0.1", "react-native-safe-area-context": "^5.5.1", "react-native-screens": "^4.11.1", "react-redux": "^9.2.0"}, "codegenConfig": {"name": "WeatherAppSpecs", "type": "all", "jsSrcsDir": "./specs", "android": {"javaPackageName": "com.weatherapp"}, "ios": {"modules": {"NativeWeatherLocation": {"className": "NativeWeatherLocation"}}, "customViewNativeComponent": {"NativeWeatherLocation": "CustomViewManager"}}}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "19.0.0", "@react-native-community/cli-platform-android": "19.0.0", "@react-native-community/cli-platform-ios": "19.0.0", "@react-native/babel-preset": "0.80.1", "@react-native/eslint-config": "0.80.1", "@react-native/metro-config": "0.80.1", "@react-native/typescript-config": "^0.80.1", "@types/jest": "^29.5.13", "@types/react": "^19.1.0", "@types/react-native": "^0.72.8", "@types/react-test-renderer": "^19.1.0", "eslint": "^8.19.0", "javascript-obfuscator": "^4.1.1", "jest": "^29.6.3", "metro-react-native-babel-transformer": "^0.77.0", "prettier": "2.8.8", "react-native-dotenv": "^3.4.11", "react-native-obfuscating-transformer": "^1.0.0", "react-test-renderer": "19.1.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}