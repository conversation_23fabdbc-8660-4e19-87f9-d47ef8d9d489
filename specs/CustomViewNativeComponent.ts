import type { HostComponent, ViewProps } from 'react-native';
import { DirectEventHandler } from 'react-native/Libraries/Types/CodegenTypes';
import codegenNativeComponent from 'react-native/Libraries/Utilities/codegenNativeComponent';

export interface CustomViewProps extends ViewProps {
  color?: string;
  onClick?: DirectEventHandler<null>;
}

export default codegenNativeComponent<CustomViewProps>(
  'CustomView',
) as HostComponent<CustomViewProps>;